import requests
import time
import json
import logging
from datetime import datetime
from typing import List, Dict

# 导入配置管理模块
from config.settings import get_settings

# ==================== 配置初始化 ====================

# 初始化设置
settings = get_settings()
print(f"配置加载完成: {settings.get_summary()}")

# ==================== 日志配置 ====================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(settings.output.log_file, encoding=settings.output.log_encoding),
        logging.StreamHandler()
    ]
)

# ==================== 主程序 ====================
def make_request(user_code: str) -> Dict:
    """发送API请求并处理重试逻辑"""
    url = f"{settings.api.base_url}?userCode={user_code}"
    for attempt in range(1, settings.processing.max_retries + 1):
        try:
            response = requests.get(
                url, 
                headers=settings.api.headers, 
                timeout=settings.processing.request_timeout
            )
            return {
                "userCode": user_code,
                "status": response.status_code,
                "success": 200 <= response.status_code < 300,
                "response": response.text,
                "attempt": attempt,
                "error": None
            }
        except requests.exceptions.RequestException as e:
            logging.warning(f"请求失败 (第 {attempt} 次): 用户 {user_code} - {e}")
            if attempt == settings.processing.max_retries:
                return {
                    "userCode": user_code,
                    "status": None,
                    "success": False,
                    "response": None,
                    "attempt": attempt,
                    "error": str(e)
                }
            time.sleep(2 ** attempt)  # 指数退避
    return {}

def main():
    """主函数：执行批量API请求"""
    try:
        # 从配置加载用户代码，从990004开始
        filtered_codes = settings.load_user_codes(start_code="990004")
    except FileNotFoundError as e:
        logging.error(f"用户代码文件未找到: {e}")
        return
    except Exception as e:
        logging.error(f"配置加载失败: {e}")
        return

    total = len(filtered_codes)
    success_count = 0
    failed_count = 0
    results = []

    logging.info(f"开始批量执行 API 请求，共 {total} 个用户代码。")

    start_time = datetime.now()

    for i, user_code in enumerate(filtered_codes, 1):
        print(f"\r处理进度: {i}/{total} ({(i/total)*100:.1f}%) - 当前用户: {user_code}", end="", flush=True)
        logging.info(f"正在处理用户: {user_code}")

        result = make_request(user_code)
        results.append(result)

        if result["success"]:
            success_count += 1
            logging.info(f"✅ 成功 - 用户 {user_code} | 状态码: {result['status']}")
        else:
            failed_count += 1
            logging.error(f"❌ 失败 - 用户 {user_code} | 错误: {result['error'] or f'HTTP {result['status']}'}")

        time.sleep(settings.processing.delay_between_requests)

    # 结束进度显示
    print("\n")

    end_time = datetime.now()
    duration = end_time - start_time

    # 保存响应结果
    with open(settings.output.responses_file, 'w', encoding=settings.output.log_encoding) as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    # 生成摘要报告
    report = f"""
==================== 执行摘要报告 ====================
开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}
结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
总耗时: {duration}
总请求数: {total}
成功数: {success_count}
失败数: {failed_count}
成功率: {success_count / total * 100:.1f}%
结果文件: {settings.output.responses_file}
日志文件: {settings.output.log_file}
====================================================
"""
    with open(settings.output.report_file, 'w', encoding=settings.output.log_encoding) as f:
        f.write(report)

    print(report)
    logging.info("批量 API 请求任务已完成。")

if __name__ == "__main__":
    main()