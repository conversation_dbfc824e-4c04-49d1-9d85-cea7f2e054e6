#!/usr/bin/env python3
"""
批处理API运行脚本
用于执行批量用户数据同步任务
"""

import sys
import argparse
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from py_test.api.runner import <PERSON>ch<PERSON>unner
from py_test.utils.logger import setup_logging


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量API处理工具")
    parser.add_argument(
        "--start-code", 
        help="指定开始处理的用户代码", 
        type=str
    )
    
    args = parser.parse_args()
    
    # 设置日志
    logger = setup_logging()
    logger.info("批处理任务开始")
    
    try:
        # 创建并运行批处理器
        runner = BatchRunner()
        stats = runner.run_batch(start_code=args.start_code)
        
        logger.info(f"批处理任务完成: {stats}")
        print(f"\n✅ 任务完成!")
        print(f"总计: {stats['total']}, 成功: {stats['success']}, 失败: {stats['failed']}")
        
    except Exception as e:
        logger.error(f"批处理任务失败: {e}")
        print(f"\n❌ 任务失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()