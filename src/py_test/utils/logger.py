"""
日志配置工具
"""

import logging
from pathlib import Path
from ..config.settings import get_settings


def setup_logging():
    """设置项目日志配置"""
    settings = get_settings()
    
    # 确保日志目录存在
    log_path = Path.cwd() / "logs" / settings.output.log_file
    log_path.parent.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_path, encoding=settings.output.log_encoding),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)