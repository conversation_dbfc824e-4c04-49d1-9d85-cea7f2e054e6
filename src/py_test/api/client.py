"""
API客户端模块
提供HTTP请求客户端和重试机制
"""

import requests
import time
import logging
from typing import Dict
from ..config.settings import get_settings


class APIClient:
    """API客户端类，处理HTTP请求和重试逻辑"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = logging.getLogger(__name__)
    
    def make_request(self, user_code: str) -> Dict:
        """发送API请求并处理重试逻辑"""
        url = f"{self.settings.api.base_url}?userCode={user_code}"
        
        for attempt in range(1, self.settings.processing.max_retries + 1):
            try:
                response = requests.get(
                    url, 
                    headers=self.settings.api.headers, 
                    timeout=self.settings.processing.request_timeout
                )
                return {
                    "userCode": user_code,
                    "status": response.status_code,
                    "success": 200 <= response.status_code < 300,
                    "response": response.text,
                    "attempt": attempt,
                    "error": None
                }
            except requests.exceptions.RequestException as e:
                self.logger.warning(f"请求失败 (第 {attempt} 次): 用户 {user_code} - {e}")
                if attempt == self.settings.processing.max_retries:
                    return {
                        "userCode": user_code,
                        "status": None,
                        "success": False,
                        "response": None,
                        "attempt": attempt,
                        "error": str(e)
                    }
                # 指数退避
                sleep_time = 2 ** attempt
                self.logger.info(f"等待 {sleep_time} 秒后重试...")
                time.sleep(sleep_time)
        
        return {
            "userCode": user_code,
            "status": None,
            "success": False,
            "response": None,
            "attempt": self.settings.processing.max_retries,
            "error": "Max retries exceeded"
        }