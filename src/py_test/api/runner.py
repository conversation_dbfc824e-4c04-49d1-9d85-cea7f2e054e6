"""
批处理运行器模块
处理批量API请求和结果管理
"""

import json
import time
import logging
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path

from .client import APIClient
from ..config.settings import get_settings


class BatchRunner:
    """批处理运行器类"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client = APIClient()
        self.logger = logging.getLogger(__name__)
        self.responses: List[Dict] = []
    
    def run_batch(self, start_code: Optional[str] = None) -> Dict:
        """运行批量处理任务"""
        # 加载用户代码
        user_codes = self.settings.load_user_codes(start_code)
        
        if not user_codes:
            self.logger.warning("没有找到需要处理的用户代码")
            return {"total": 0, "success": 0, "failed": 0}
        
        self.logger.info(f"开始处理 {len(user_codes)} 个用户代码")
        
        # 统计信息
        stats = {"total": len(user_codes), "success": 0, "failed": 0}
        
        # 处理每个用户代码
        for i, user_code in enumerate(user_codes, 1):
            self.logger.info(f"处理用户 {user_code} ({i}/{len(user_codes)})")
            
            # 发送请求
            result = self.client.make_request(user_code)
            self.responses.append(result)
            
            # 更新统计
            if result["success"]:
                stats["success"] += 1
                self.logger.info(f"✅ 用户 {user_code} 处理成功")
            else:
                stats["failed"] += 1
                self.logger.error(f"❌ 用户 {user_code} 处理失败: {result.get('error', 'Unknown error')}")
            
            # 请求间延迟
            if i < len(user_codes):
                time.sleep(self.settings.processing.delay_between_requests)
        
        # 保存结果
        self._save_responses()
        self._generate_report(stats)
        
        return stats
    
    def _save_responses(self) -> None:
        """保存API响应到JSON文件"""
        output_path = Path.cwd() / "output" / self.settings.output.responses_file
        output_path.parent.mkdir(exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.responses, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"响应数据已保存到: {output_path}")
    
    def _generate_report(self, stats: Dict) -> None:
        """生成汇总报告"""
        output_path = Path.cwd() / "output" / self.settings.output.report_file
        output_path.parent.mkdir(exist_ok=True)
        
        report_content = f"""
批量API执行报告
================

执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

统计信息:
- 总处理数: {stats['total']}
- 成功数: {stats['success']}
- 失败数: {stats['failed']}
- 成功率: {stats['success']/stats['total']*100:.1f}%

详细结果:
"""
        
        for response in self.responses:
            status = "✅ 成功" if response["success"] else "❌ 失败"
            report_content += f"- 用户 {response['userCode']}: {status}"
            if not response["success"] and response.get("error"):
                report_content += f" ({response['error']})"
            report_content += "\n"
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        self.logger.info(f"执行报告已生成: {output_path}")