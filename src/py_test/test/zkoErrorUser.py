#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解析阿里云日志服务返回的JSON数据，提取syncEmployee2Zkteco消息中的userId编码
支持直接从curl命令获取数据并解析
"""

import json
import re
import subprocess
import shlex
from typing import List, Dict, Any
import requests


class ZkoErrorUserParser:
    """解析器类，用于从阿里云日志中提取userId编码"""
    
    def __init__(self):
        self.user_id_pattern = re.compile(r'syncEmployee2Zkteco\s*\|\s*userId\s*:\s*(\d+)')
    
    def execute_curl_command(self, curl_command: str) -> str:
        """
        执行curl命令并返回结果
        
        Args:
            curl_command: curl命令字符串
            
        Returns:
            curl命令的输出结果
        """
        try:
            # 使用shell执行curl命令
            result = subprocess.run(
                curl_command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            if result.returncode == 0:
                return result.stdout
            else:
                print(f"curl命令执行失败: {result.stderr}")
                return ""
        except subprocess.TimeoutExpired:
            print("curl命令执行超时")
            return ""
        except Exception as e:
            print(f"执行curl命令时出错: {e}")
            return ""
    
    def execute_curl_with_requests(self, curl_command: str) -> str:
        """
        解析curl命令并使用requests库执行
        
        Args:
            curl_command: curl命令字符串
            
        Returns:
            响应内容
        """
        try:
            # 解析curl命令
            import re
            
            # 提取URL
            url_match = re.search(r"curl\s+'([^']+)'", curl_command)
            if not url_match:
                print("无法从curl命令中提取URL")
                return ""
            
            url = url_match.group(1)
            
            # 提取headers
            headers = {}
            header_matches = re.findall(r"-H\s+'([^:]+):\s*([^']+)'", curl_command)
            for key, value in header_matches:
                headers[key.strip()] = value.strip()
            
            # 提取POST数据
            data_match = re.search(r"--data-raw\s+'([^']+)'", curl_command)
            post_data = data_match.group(1) if data_match else None
            
            # 发送请求
            if post_data:
                response = requests.post(url, headers=headers, data=post_data, timeout=30)
            else:
                response = requests.get(url, headers=headers, timeout=30)
            
            response.raise_for_status()
            return response.text
            
        except Exception as e:
            print(f"使用requests执行请求时出错: {e}")
            return ""
    
    def parse_json_response(self, json_data: str) -> List[str]:
        """
        解析JSON响应字符串，提取所有userId编码
        
        Args:
            json_data: JSON格式的响应字符串
            
        Returns:
            包含所有userId编码的列表
        """
        if not json_data:
            return []
            
        try:
            data = json.loads(json_data)
            return self.extract_user_ids_from_data(data)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return []
    
    def extract_user_ids_from_data(self, data: Dict[str, Any]) -> List[str]:
        """
        从解析后的JSON数据中提取userId编码
        
        Args:
            data: 解析后的JSON数据
            
        Returns:
            包含所有userId编码的列表
        """
        user_ids = []
        
        if not isinstance(data, dict):
            return user_ids
            
        # 获取data数组
        log_entries = data.get('data', [])
        
        for entry in log_entries:
            if isinstance(entry, dict) and 'message' in entry:
                message = entry['message']
                user_id = self.extract_user_id_from_message(message)
                if user_id:
                    user_ids.append(user_id)
        
        return user_ids
    
    def extract_user_id_from_message(self, message: str) -> str:
        """
        从单个消息中提取userId编码
        
        Args:
            message: 消息字符串
            
        Returns:
            提取到的userId编码，如果未找到则返回空字符串
        """
        if not message:
            return ""
            
        match = self.user_id_pattern.search(message)
        if match:
            return match.group(1)
        
        return ""
    
    def parse_from_curl(self, curl_command: str, use_requests: bool = True) -> List[str]:
        """
        从curl命令直接获取数据并解析user