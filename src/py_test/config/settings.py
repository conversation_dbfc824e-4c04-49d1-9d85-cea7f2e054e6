"""
配置管理模块
提供环境变量管理和配置验证功能
"""

import os
import logging
from pathlib import Path
from typing import List, Optional
from dataclasses import dataclass

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logging.warning("python-dotenv not installed. Environment variables will only be read from system.")


@dataclass
class APIConfig:
    """API配置类"""
    base_url: str
    authorization_token: str
    user_agent: str = "Apifox/1.0.0 (https://apifox.com)"
    host: str = "oa.imile.com"
    connection: str = "keep-alive"
    accept: str = "*/*"
    
    @property
    def headers(self) -> dict:
        """返回HTTP请求头"""
        return {
            "User-Agent": self.user_agent,
            "Authorization": f"Bearer {self.authorization_token}",
            "Accept": self.accept,
            "Host": self.host,
            "Connection": self.connection
        }


@dataclass
class ProcessingConfig:
    """处理配置类"""
    delay_between_requests: float = 1.0
    max_retries: int = 3
    request_timeout: int = 10
    skip_codes: set = None
    
    def __post_init__(self):
        if self.skip_codes is None:
            self.skip_codes = set()


@dataclass
class OutputConfig:
    """输出配置类"""
    log_file: str = "api_execution.log"
    responses_file: str = "responses.json"
    report_file: str = "summary_report.txt"
    log_encoding: str = "utf-8"


class Settings:
    """应用设置管理类"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent.parent
        self._validate_environment()
        
        # 初始化配置
        self.api = self._load_api_config()
        self.processing = self._load_processing_config()
        self.output = self._load_output_config()
        
    def _validate_environment(self) -> None:
        """验证必需的环境变量"""
        required_vars = [
            "API_BASE_URL",
            "API_AUTHORIZATION_TOKEN"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_vars)}. "
                f"Please check your .env file or environment configuration."
            )
    
    def _load_api_config(self) -> APIConfig:
        """加载API配置"""
        return APIConfig(
            base_url=os.getenv("API_BASE_URL"),
            authorization_token=os.getenv("API_AUTHORIZATION_TOKEN"),
            user_agent=os.getenv("API_USER_AGENT", "Apifox/1.0.0 (https://apifox.com)"),
            host=os.getenv("API_HOST", "oa.imile.com"),
            connection=os.getenv("API_CONNECTION", "keep-alive"),
            accept=os.getenv("API_ACCEPT", "*/*")
        )
    
    def _load_processing_config(self) -> ProcessingConfig:
        """加载处理配置"""
        skip_codes_str = os.getenv("SKIP_USER_CODES", "")
        skip_codes = set(skip_codes_str.split(",")) if skip_codes_str else set()
        
        return ProcessingConfig(
            delay_between_requests=float(os.getenv("DELAY_BETWEEN_REQUESTS", "1.0")),
            max_retries=int(os.getenv("MAX_RETRIES", "3")),
            request_timeout=int(os.getenv("REQUEST_TIMEOUT", "10")),
            skip_codes=skip_codes
        )
    
    def _load_output_config(self) -> OutputConfig:
        """加载输出配置"""
        return OutputConfig(
            log_file=os.getenv("LOG_FILE", "api_execution.log"),
            responses_file=os.getenv("RESPONSES_FILE", "responses.json"),
            report_file=os.getenv("REPORT_FILE", "summary_report.txt"),
            log_encoding=os.getenv("LOG_ENCODING", "utf-8")
        )
    
    def load_user_codes(self, start_code: Optional[str] = None) -> List[str]:
        """
        从数据文件加载用户代码列表
        
        Args:
            start_code: 开始处理的用户代码，如果指定则从该代码开始
            
        Returns:
            过滤后的用户代码列表
        """
        user_codes_file = os.getenv("USER_CODES_FILE", "data/user_codes.txt")
        user_codes_path = self.project_root / user_codes_file
        
        if not user_codes_path.exists():
            raise FileNotFoundError(f"User codes file not found: {user_codes_path}")
        
        # 读取用户代码
        with open(user_codes_path, 'r', encoding='utf-8') as f:
            user_codes = [line.strip() for line in f if line.strip()]
        
        # 应用开始位置过滤
        if start_code and start_code in user_codes:
            start_index = user_codes.index(start_code)
            user_codes = user_codes[start_index:]
        
        # 过滤跳过的代码
        filtered_codes = [code for code in user_codes if code not in self.processing.skip_codes]
        
        return filtered_codes
    
    def get_summary(self) -> str:
        """返回配置摘要信息"""
        return f"""
配置摘要:
- API端点: {self.api.base_url}
- 请求延迟: {self.processing.delay_between_requests}秒
- 最大重试: {self.processing.max_retries}次
- 请求超时: {self.processing.request_timeout}秒
- 跳过代码数: {len(self.processing.skip_codes)}
- 日志文件: {self.output.log_file}
        """.strip()


# 全局设置实例
settings = None

def get_settings() -> Settings:
    """获取设置实例（单例模式）"""
    global settings
    if settings is None:
        settings = Settings()
    return settings