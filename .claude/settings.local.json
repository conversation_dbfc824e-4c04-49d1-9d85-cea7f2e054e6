{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(chmod:*)", "Bash(python tests/test_config.py)", "Bash(uv sync:*)", "Bash(cp /Users/<USER>/IdeaProjects/py-test/.env.example /Users/<USER>/IdeaProjects/py-test/.env)", "Bash(export API_BASE_URL=https://oa.imile.com/oa/asset/sync/user)", "Bash(export API_AUTHORIZATION_TOKEN=demo_token_for_testing_purposes_only)", "<PERSON><PERSON>(python:*)", "Bash(pip install:*)", "<PERSON><PERSON>(uv run:*)", "Bash(uv add:*)", "Bash(PYTHONPATH=src python:*)"], "deny": []}}