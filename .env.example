# 环境变量配置模板
# 复制此文件为 .env 并填入实际值

# ===========================================
# API 配置 (必需)
# ===========================================
API_BASE_URL=https://oa.imile.com/oa/asset/sync/user
API_AUTHORIZATION_TOKEN=your_bearer_token_here

# API 请求头配置 (可选)
API_USER_AGENT=Apifox/1.0.0 (https://apifox.com)
API_HOST=oa.imile.com
API_CONNECTION=keep-alive
API_ACCEPT=*/*

# ===========================================
# 处理配置 (可选)
# ===========================================
# 请求间延迟 (秒)
DELAY_BETWEEN_REQUESTS=1.0

# 最大重试次数
MAX_RETRIES=3

# 请求超时 (秒)
REQUEST_TIMEOUT=10

# 跳过的用户代码 (逗号分隔)
SKIP_USER_CODES=990059

# ===========================================
# 数据文件配置 (可选)
# ===========================================
# 用户代码文件路径
USER_CODES_FILE=data/user_codes.txt

# ===========================================
# 输出文件配置 (可选)
# ===========================================
LOG_FILE=api_execution.log
RESPONSES_FILE=responses.json
REPORT_FILE=summary_report.txt
LOG_ENCODING=utf-8