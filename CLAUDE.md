# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

A Python project for batch API operations, specifically designed for syncing user data to an OA (Office Automation) system. The project implements a robust batch processing system with retry mechanisms, logging, and comprehensive reporting following Python packaging standards.

## Architecture

The codebase follows modern Python project structure with clear separation of concerns:

- **src/py_test/**: Main package containing all source code
  - **api/**: API client and batch processing logic
  - **config/**: Configuration management with environment variables
  - **utils/**: Utility functions and logging setup
- **scripts/**: Executable scripts for running batch operations
- **tests/**: Unit tests and configuration validation
- **data/**: Input data files (user codes)
- **output/**: Generated output files (responses, reports)
- **logs/**: Application logs
- **docs/**: Documentation and learning materials

## Key Components

### src/py_test/api/
- **client.py**: HTTP API client with retry mechanism and error handling
- **runner.py**: Batch processing orchestrator with progress tracking and reporting

### src/py_test/config/
- **settings.py**: Environment-based configuration management with validation

### scripts/
- **run_batch.py**: Main execution script with command-line interface
- **legacy_batch_api_runner.py**: Original implementation (for reference)

## Development Commands

### Running the Application
```bash
# Using the new modular script
python scripts/run_batch.py

# With start code parameter
python scripts/run_batch.py --start-code USER123

# Using uv (recommended)
uv run python scripts/run_batch.py
```

### Development Setup
```bash
# Install dependencies
uv sync

# Copy environment template and configure
cp .env.example .env
# Edit .env with your API credentials

# Run configuration tests
python tests/test_config.py

# Run API tests
python tests/test_api.py
```

### Project Structure
```
py-test/
├── src/py_test/              # Main package
│   ├── __init__.py
│   ├── api/                  # API modules
│   │   ├── __init__.py
│   │   ├── client.py         # HTTP client
│   │   └── runner.py         # Batch runner
│   ├── config/               # Configuration
│   │   ├── __init__.py
│   │   └── settings.py       # Settings management
│   └── utils/                # Utilities
│       ├── __init__.py
│       └── logger.py         # Logging setup
├── scripts/                  # Executable scripts
│   └── run_batch.py          # Main runner script
├── tests/                    # Test modules
│   ├── __init__.py
│   ├── test_api.py           # API tests
│   └── test_config.py        # Config validation
├── data/                     # Input data
│   └── user_codes.txt        # User codes list
├── output/                   # Generated files
├── logs/                     # Application logs
├── docs/                     # Documentation
├── .env.example              # Environment template
├── pyproject.toml            # Project configuration
└── README.md                 # Project documentation
```

## Configuration

### Environment Variables
Create a `.env` file from `.env.example`:

```bash
# Required
API_BASE_URL=https://oa.imile.com/oa/asset/sync/user
API_AUTHORIZATION_TOKEN=your_token_here

# Optional (with defaults)
DELAY_BETWEEN_REQUESTS=1.0
MAX_RETRIES=3
REQUEST_TIMEOUT=10
USER_CODES_FILE=data/user_codes.txt
```

### Key Features

- **Modular Architecture**: Clear separation between API client, batch runner, and configuration
- **Environment-Based Config**: Secure configuration via environment variables
- **Comprehensive Testing**: Configuration validation and API testing
- **Progress Tracking**: Real-time progress reporting with detailed logs
- **Error Handling**: Robust retry mechanism with exponential backoff
- **Output Management**: Organized output files (logs, responses, reports)

## Development Patterns

- **Package Structure**: Following `src/` layout for proper packaging
- **Configuration**: Environment variables with `.env` support via python-dotenv
- **Logging**: Structured logging to both file and console
- **Testing**: Comprehensive test suite for validation
- **CLI Interface**: Argument parsing with help text

## Quick Start

1. **Setup Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your API credentials
   ```

2. **Install Dependencies**:
   ```bash
   uv sync
   ```

3. **Validate Configuration**:
   ```bash
   python tests/test_config.py
   ```

4. **Run Batch Process**:
   ```bash
   python scripts/run_batch.py
   ```

5. **Check Results**:
   - Logs: `logs/api_execution.log`
   - Responses: `output/responses.json`
   - Report: `output/summary_report.txt`