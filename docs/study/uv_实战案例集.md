# uv Python 包管理工具实战案例集

## 📚 案例目录

1. [CLI 工具开发](#cli-工具开发)
2. [机器学习项目](#机器学习项目)
3. [微服务项目](#微服务项目)
4. [爬虫项目](#爬虫项目)
5. [测试驱动开发 (TDD)](#测试驱动开发-tdd)
6. [配置管理项目](#配置管理项目)
7. [异步任务队列](#异步任务队列)
8. [Docker 容器化项目](#docker-容器化项目)
9. [GraphQL API 项目](#graphql-api-项目)
10. [实时聊天应用](#实时聊天应用)

---

## CLI 工具开发

### 创建功能丰富的命令行工具

```bash
# 1. 创建 CLI 工具项目
uv init --app my-cli-tool
cd my-cli-tool

# 2. 添加 CLI 相关依赖
uv add click rich typer

# 3. 添加开发和测试依赖
uv add --dev pytest pytest-cov black ruff mypy

# 4. 创建 CLI 入口文件
cat > src/my_cli_tool/cli.py << 'EOF'
import click
from rich.console import Console
from rich.table import Table
from rich.progress import track
import time

console = Console()

@click.group()
def cli():
    """功能强大的 CLI 工具示例"""
    pass

@cli.command()
@click.option('--name', default='World', help='要问候的名字')
@click.option('--count', default=1, help='问候次数')
def hello(name, count):
    """问候命令"""
    for i in track(range(count), description="问候中..."):
        console.print(f"Hello {name}! ({i+1}/{count})", style="bold green")
        time.sleep(0.5)

@cli.command()
@click.argument('files', nargs=-1, type=click.Path(exists=True))
def analyze(files):
    """分析文件"""
    table = Table(title="文件分析结果")
    table.add_column("文件名", style="cyan")
    table.add_column("大小", style="magenta")
    table.add_column("类型", style="green")
    
    for file_path in files:
        import os
        size = os.path.getsize(file_path)
        file_type = file_path.split('.')[-1] if '.' in file_path else 'unknown'
        table.add_row(os.path.basename(file_path), f"{size} bytes", file_type)
    
    console.print(table)

if __name__ == '__main__':
    cli()
EOF

# 5. 配置项目入口点
cat >> pyproject.toml << 'EOF'

[project.scripts]
my-cli = "my_cli_tool.cli:cli"
EOF

# 6. 安装项目（可编辑模式）
uv sync

# 7. 运行 CLI 工具
uv run my-cli hello --name "开发者" --count 3
uv run my-cli analyze pyproject.toml
```

---

## 机器学习项目

### 完整的机器学习工作流

```bash
# 1. 创建 ML 项目
uv init ml-project
cd ml-project

# 2. 添加机器学习依赖
uv add scikit-learn pandas numpy matplotlib seaborn

# 3. 添加深度学习依赖（可选组）
uv add --optional deep torch torchvision tensorflow

# 4. 添加开发依赖
uv add --dev jupyter notebook pytest black ruff mypy

# 5. 创建数据处理模块
mkdir -p src/ml_project
cat > src/ml_project/data_processor.py << 'EOF'
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

class DataProcessor:
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoders = {}
    
    def load_data(self, file_path):
        """加载数据"""
        if file_path.endswith('.csv'):
            return pd.read_csv(file_path)
        elif file_path.endswith('.json'):
            return pd.read_json(file_path)
        else:
            raise ValueError("不支持的文件格式")
    
    def explore_data(self, data):
        """数据探索"""
        print("=== 数据基本信息 ===")
        print(f"数据形状: {data.shape}")
        print(f"列名: {list(data.columns)}")
        print(f"数据类型:\n{data.dtypes}")
        print(f"缺失值:\n{data.isnull().sum()}")
        
        # 数值型列的统计信息
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            print(f"\n=== 数值型列统计 ===")
            print(data[numeric_cols].describe())
        
        return data.info()
    
    def preprocess(self, data, target_column, test_size=0.2):
        """数据预处理"""
        # 处理缺失值
        data = data.fillna(data.mean(numeric_only=True))
        
        # 编码分类变量
        categorical_cols = data.select_dtypes(include=['object']).columns
        categorical_cols = categorical_cols.drop(target_column, errors='ignore')
        
        for col in categorical_cols:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
            data[col] = self.label_encoders[col].fit_transform(data[col].astype(str))
        
        # 分离特征和目标
        X = data.drop(columns=[target_column])
        y = data[target_column]
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=y
        )
        
        # 标准化特征
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        return X_train_scaled, X_test_scaled, y_train, y_test
    
    def plot_results(self, y_true, y_pred, model_name="模型"):
        """绘制结果"""
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        # 混淆矩阵
        cm = confusion_matrix(y_true, y_pred)
        sns.heatmap(cm, annot=True, fmt='d', ax=axes[0])
        axes[0].set_title(f'{model_name} - 混淆矩阵')
        axes[0].set_xlabel('预测值')
        axes[0].set_ylabel('真实值')
        
        # 分类报告
        report = classification_report(y_true, y_pred, output_dict=True)
        report_df = pd.DataFrame(report).iloc[:-1, :].T
        sns.heatmap(report_df.iloc[:, :-1], annot=True, ax=axes[1])
        axes[1].set_title(f'{model_name} - 分类报告')
        
        plt.tight_layout()
        plt.show()
        
        return classification_report(y_true, y_pred)
EOF

# 6. 创建模型训练脚本
cat > src/ml_project/trainer.py << 'EOF'
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, classification_report
import joblib
import os

class ModelTrainer:
    def __init__(self):
        self.models = {
            'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'logistic_regression': LogisticRegression(random_state=42),
            'svm': SVC(random_state=42)
        }
        self.trained_models = {}
    
    def train_all_models(self, X_train, y_train):
        """训练所有模型"""
        for name, model in self.models.items():
            print(f"训练 {name}...")
            model.fit(X_train, y_train)
            self.trained_models[name] = model
            print(f"{name} 训练完成")
    
    def evaluate_models(self, X_test, y_test):
        """评估所有模型"""
        results = {}
        for name, model in self.trained_models.items():
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            results[name] = {
                'accuracy': accuracy,
                'predictions': y_pred,
                'report': classification_report(y_test, y_pred)
            }
            print(f"\n{name} 准确率: {accuracy:.4f}")
            print(f"{name} 分类报告:\n{results[name]['report']}")
        
        return results
    
    def save_best_model(self, results, model_dir="models"):
        """保存最佳模型"""
        os.makedirs(model_dir, exist_ok=True)
        
        # 找到最佳模型
        best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
        best_model = self.trained_models[best_model_name]
        
        # 保存模型
        model_path = os.path.join(model_dir, f"best_model_{best_model_name}.joblib")
        joblib.dump(best_model, model_path)
        
        print(f"最佳模型 {best_model_name} 已保存到: {model_path}")
        return model_path, best_model_name
EOF

# 7. 创建完整的训练脚本
cat > train_model.py << 'EOF'
from src.ml_project.data_processor import DataProcessor
from src.ml_project.trainer import ModelTrainer
import pandas as pd
from sklearn.datasets import make_classification

def main():
    # 创建示例数据集
    print("创建示例数据集...")
    X, y = make_classification(
        n_samples=1000, n_features=20, n_informative=10,
        n_redundant=10, n_classes=3, random_state=42
    )
    
    # 转换为 DataFrame
    feature_names = [f'feature_{i}' for i in range(X.shape[1])]
    data = pd.DataFrame(X, columns=feature_names)
    data['target'] = y
    
    # 保存数据
    data.to_csv('sample_data.csv', index=False)
    print("示例数据已保存到 sample_data.csv")
    
    # 初始化处理器和训练器
    processor = DataProcessor()
    trainer = ModelTrainer()
    
    # 数据探索
    print("\n=== 数据探索 ===")
    processor.explore_data(data)
    
    # 数据预处理
    print("\n=== 数据预处理 ===")
    X_train, X_test, y_train, y_test = processor.preprocess(data, 'target')
    
    # 训练模型
    print("\n=== 模型训练 ===")
    trainer.train_all_models(X_train, y_train)
    
    # 评估模型
    print("\n=== 模型评估 ===")
    results = trainer.evaluate_models(X_test, y_test)
    
    # 保存最佳模型
    print("\n=== 保存模型 ===")
    trainer.save_best_model(results)
    
    print("\n训练完成！")

if __name__ == "__main__":
    main()
EOF

# 8. 运行训练
uv run python train_model.py

# 9. 启动 Jupyter 进行进一步分析
uv run jupyter notebook
```

---

## 微服务项目

### 构建完整的微服务架构

```bash
# 1. 创建微服务项目
uv init --app user-service
cd user-service

# 2. 添加微服务相关依赖
uv add fastapi uvicorn sqlalchemy alembic pydantic-settings

# 3. 添加数据库和缓存依赖
uv add psycopg2-binary redis

# 4. 添加开发和测试依赖
uv add --dev pytest pytest-asyncio httpx black ruff mypy

# 5. 创建项目结构
mkdir -p src/user_service/{api,models,services,database,schemas}

# 6. 创建数据库配置
cat > src/user_service/database/config.py << 'EOF'
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./users.db")

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
EOF

# 7. 创建用户模型
cat > src/user_service/models/user.py << 'EOF'
from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.sql import func
from ..database.config import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    full_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
EOF

# 8. 创建 Pydantic 模式
cat > src/user_service/schemas/user.py << 'EOF'
from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional

class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    pass

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None

class UserResponse(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
EOF

# 9. 创建用户服务
cat > src/user_service/services/user_service.py << 'EOF'
from sqlalchemy.orm import Session
from ..models.user import User
from ..schemas.user import UserCreate, UserUpdate
from typing import List, Optional

class UserService:
    def __init__(self, db: Session):
        self.db = db
    
    def create_user(self, user_data: UserCreate) -> User:
        """创建用户"""
        db_user = User(**user_data.dict())
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        return db_user
    
    def get_user(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return self.db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return self.db.query(User).filter(User.username == username).first()
    
    def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return self.db.query(User).filter(User.email == email).first()
    
    def get_users(self, skip: int = 0, limit: int = 100) -> List[User]:
        """获取用户列表"""
        return self.db.query(User).offset(skip).limit(limit).all()
    
    def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[User]:
        """更新用户"""
        db_user = self.get_user(user_id)
        if not db_user:
            return None
        
        update_data = user_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_user, field, value)
        
        self.db.commit()
        self.db.refresh(db_user)
        return db_user
    
    def delete_user(self, user_id: int) -> bool:
        """删除用户"""
        db_user = self.get_user(user_id)
        if not db_user:
            return False
        
        self.db.delete(db_user)
        self.db.commit()
        return True
EOF

# 10. 创建 API 路由
cat > src/user_service/api/users.py << 'EOF'
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from ..database.config import get_db
from ..services.user_service import UserService
from ..schemas.user import UserCreate, UserUpdate, UserResponse

router = APIRouter(prefix="/users", tags=["users"])

@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(user_data: UserCreate, db: Session = Depends(get_db)):
    """创建新用户"""
    user_service = UserService(db)
    
    # 检查用户名是否已存在
    if user_service.get_user_by_username(user_data.username):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    if user_service.get_user_by_email(user_data.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    return user_service.create_user(user_data)

@router.get("/{user_id}", response_model=UserResponse)
async def get_user(user_id: int, db: Session = Depends(get_db)):
    """获取用户详情"""
    user_service = UserService(db)
    user = user_service.get_user(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return user

@router.get("/", response_model=List[UserResponse])
async def get_users(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取用户列表"""
    user_service = UserService(db)
    return user_service.get_users(skip=skip, limit=limit)

@router.put("/{user_id}", response_model=UserResponse)
async def update_user(user_id: int, user_data: UserUpdate, db: Session = Depends(get_db)):
    """更新用户信息"""
    user_service = UserService(db)
    user = user_service.update_user(user_id, user_data)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    return user

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(user_id: int, db: Session = Depends(get_db)):
    """删除用户"""
    user_service = UserService(db)
    
    if not user_service.delete_user(user_id):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
EOF

# 11. 创建主应用
cat > src/user_service/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .api.users import router as users_router
from .database.config import engine, Base

# 创建数据库表
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="用户服务",
    description="用户管理微服务",
    version="1.0.0"
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(users_router, prefix="/api/v1")

@app.get("/")
async def root():
    return {"message": "用户服务正在运行", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "user-service"}
EOF

# 12. 创建测试文件
cat > tests/test_users.py << 'EOF'
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from src.user_service.main import app
from src.user_service.database.config import get_db, Base

# 创建测试数据库
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)

def test_create_user():
    """测试创建用户"""
    response = client.post(
        "/api/v1/users/",
        json={
            "username": "testuser",
            "email": "<EMAIL>",
            "full_name": "Test User"
        }
    )
    assert response.status_code == 201
    data = response.json()
    assert data["username"] == "testuser"
    assert data["email"] == "<EMAIL>"
    assert "id" in data

def test_get_user():
    """测试获取用户"""
    # 先创建用户
    create_response = client.post(
        "/api/v1/users/",
        json={
            "username": "getuser",
            "email": "<EMAIL>"
        }
    )
    user_id = create_response.json()["id"]
    
    # 获取用户
    response = client.get(f"/api/v1/users/{user_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == "getuser"

def test_get_users():
    """测试获取用户列表"""
    response = client.get("/api/v1/users/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
EOF

# 13. 运行微服务
uv run uvicorn src.user_service.main:app --reload --port 8001

# 14. 运行测试
uv run pytest tests/ -v
```

---

## 爬虫项目

### 构建高效的网页爬虫系统

```bash
# 1. 创建爬虫项目
uv init web-scraper
cd web-scraper

# 2. 添加爬虫相关依赖
uv add requests beautifulsoup4 scrapy selenium

# 3. 添加数据处理依赖
uv add pandas openpyxl

# 4. 添加异步支持
uv add aiohttp asyncio

# 5. 添加开发依赖
uv add --dev pytest black ruff mypy

# 6. 创建基础爬虫类
cat > src/web_scraper/base_scraper.py << 'EOF'
import requests
from bs4 import BeautifulSoup
import pandas as pd
from typing import List, Dict, Optional
import time
import random
from urllib.parse import urljoin, urlparse
import logging

class BaseScraper:
    def __init__(self, base_url: str, delay_range: tuple = (1, 3)):
        self.base_url = base_url
        self.delay_range = delay_range
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def get_page(self, url: str, **kwargs) -> Optional[requests.Response]:
        """获取页面内容"""
        try:
            response = self.session.get(url, **kwargs)
            response.raise_for_status()
            
            # 随机延迟
            delay = random.uniform(*self.delay_range)
            time.sleep(delay)
            
            return response
        except requests.RequestException as e:
            self.logger.error(f"获取页面失败 {url}: {e}")
            return None
    
    def parse_page(self, response: requests.Response) -> Dict:
        """解析页面内容 - 子类需要重写此方法"""
        soup = BeautifulSoup(response.content, 'html.parser')
        return {
            'url': response.url,
            'title': soup.find('title').text.strip() if soup.find('title') else '',
            'status': 'success'
        }
    
    def scrape_url(self, url: str) -> Dict:
        """爬取单个URL"""
        self.logger.info(f"正在爬取: {url}")
        response = self.get_page(url)
        
        if response:
            return self.parse_page(response)
        else:
            return {
                'url': url,
                'status': 'failed',
                'error': 'Failed to fetch page'
            }
    
    def scrape_urls(self, urls: List[str]) -> pd.DataFrame:
        """批量爬取URL"""
        results = []
        total = len(urls)
        
        for i, url in enumerate(urls, 1):
            self.logger.info(f"进度: {i}/{total}")
            result = self.scrape_url(url)
            results.append(result)
        
        return pd.DataFrame(results)
    
    def save_results(self, data: pd.DataFrame, filename: str):
        """保存结果"""
        if filename.endswith('.csv'):
            data.to_csv(filename, index=False, encoding='utf-8-sig')
        elif filename.endswith('.xlsx'):
            data.to_excel(filename, index=False)
        else:
            raise ValueError("不支持的文件格式")
        
        self.logger.info(f"结果已保存到: {filename}")
EOF

# 7. 创建新闻爬虫示例
cat > src/web_scraper/news_scraper.py << 'EOF'
from .base_scraper import BaseScraper
from bs4 import BeautifulSoup
import requests
from typing import Dict, List
from urllib.parse import urljoin

class NewsScraper(BaseScraper):
    def __init__(self):
        super().__init__("https://news.ycombinator.com")
    
    def parse_page(self, response: requests.Response) -> Dict:
        """解析 Hacker News 页面"""
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 查找新闻条目
        stories = []
        story_rows = soup.find_all('tr', class_='athing')
        
        for story in story_rows[:10]:  # 只取前10条
            try:
                # 获取标题和链接
                title_cell = story.find('span', class_='titleline')
                if title_cell:
                    title_link = title_cell.find('a')
                    title = title_link.text.strip() if title_link else ''
                    link = title_link.get('href', '') if title_link else ''
                    
                    # 获取分数和评论数（在下一行）
                    next_row = story.find_next_sibling('tr')
                    score = 0
                    comments = 0
                    
                    if next_row:
                        score_span = next_row.find('span', class_='score')
                        if score_span:
                            score = int(score_span.text.split()[0])
                        
                        comment_link = next_row.find('a', string=lambda x: x and 'comment' in x)
                        if comment_link:
                            comment_text = comment_link.text
                            comments = int(comment_text.split()[0]) if comment_text.split()[0].isdigit() else 0
                    
                    stories.append({
                        'title': title,