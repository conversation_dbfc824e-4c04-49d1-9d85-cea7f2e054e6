# uv Python 包管理工具学习指南

## 📚 目录

1. [uv 简介](#uv-简介)
2. [安装 uv](#安装-uv)
3. [基础概念](#基础概念)
4. [项目初始化](#项目初始化)
5. [依赖管理](#依赖管理)
6. [虚拟环境操作](#虚拟环境操作)
7. [构建和发布](#构建和发布)
8. [常用命令速查](#常用命令速查)
9. [实际使用示例](#实际使用示例)
10. [最佳实践](#最佳实践)
11. [常见问题解决](#常见问题解决)

---

## uv 简介

uv 是由 Astral 开发的极速 Python 包管理工具，用 Rust 编写，旨在替代 pip、pip-tools、pipx、poetry、pyenv、virtualenv 等工具。

### 🚀 主要优势

- **极速性能**：比 pip 快 10-100 倍
- **统一工具**：集成包管理、虚拟环境、项目管理于一体
- **兼容性强**：与现有 Python 生态系统完全兼容
- **内存高效**：使用 Rust 编写，内存占用低
- **跨平台**：支持 Windows、macOS、Linux

---

## 安装 uv

### 通过官方安装脚本（推荐）
```bash
# macOS 和 Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 通过包管理器
```bash
# macOS (Homebrew)
brew install uv

# Windows (Scoop)
scoop install uv

# 通过 pip 安装
pip install uv
```

### 验证安装
```bash
uv --version
```

---

## 基础概念

### 核心组件
- **项目 (Project)**：包含 `pyproject.toml` 的 Python 项目
- **虚拟环境 (Virtual Environment)**：隔离的 Python 环境
- **锁文件 (Lock File)**：`uv.lock` 文件，记录精确的依赖版本
- **工作区 (Workspace)**：包含多个相关项目的集合

### 配置文件
- `pyproject.toml`：项目配置和依赖声明
- `uv.lock`：锁定的依赖版本（自动生成）
- `uv.toml`：uv 工具配置文件

---

## 项目初始化

### 创建新项目
```bash
# 创建新的 Python 项目
uv init my-project
cd my-project

# 创建应用程序项目
uv init --app my-app

# 创建库项目
uv init --lib my-library

# 在现有目录初始化
uv init
```

### 项目结构
```
my-project/
├── pyproject.toml      # 项目配置
├── uv.lock            # 锁文件
├── README.md          # 项目说明
├── src/               # 源代码目录
│   └── my_project/
│       └── __init__.py
└── tests/             # 测试目录
```

---

## 依赖管理

### 添加依赖
```bash
# 添加运行时依赖
uv add requests
uv add "django>=4.0"
uv add "numpy>=1.20,<2.0"

# 添加开发依赖
uv add --dev pytest
uv add --dev black ruff mypy

# 添加可选依赖组
uv add --optional web fastapi uvicorn
uv add --optional dev pytest black ruff

# 从 requirements.txt 添加
uv add -r requirements.txt
```

### 移除依赖
```bash
# 移除依赖
uv remove requests
uv remove --dev pytest

# 移除可选依赖组
uv remove --optional web fastapi
```

### 安装依赖
```bash
# 安装所有依赖
uv sync

# 只安装生产依赖
uv sync --no-dev

# 安装特定可选依赖组
uv sync --extra web

# 安装所有可选依赖
uv sync --all-extras
```

### 更新依赖
```bash
# 更新所有依赖
uv lock --upgrade

# 更新特定包
uv lock --upgrade-package requests

# 查看过期的包
uv tree --outdated
```

---

## 虚拟环境操作

### 创建和激活虚拟环境
```bash
# 创建虚拟环境
uv venv

# 指定 Python 版本
uv venv --python 3.11
uv venv --python python3.12

# 指定虚拟环境位置
uv venv .venv
uv venv /path/to/venv

# 激活虚拟环境
# Linux/macOS
source .venv/bin/activate
# Windows
.venv\Scripts\activate
```

### 在虚拟环境中运行命令
```bash
# 直接在项目环境中运行
uv run python script.py
uv run pytest
uv run python -m mymodule

# 运行 Python 脚本
uv run --script my_script.py

# 临时安装并运行工具
uv tool run black .
uv tool run ruff check .
```

---

## 构建和发布

### 构建项目
```bash
# 构建分发包
uv build

# 构建到指定目录
uv build --out-dir dist/

# 只构建 wheel
uv build --wheel

# 只构建源码分发
uv build --sdist
```

### 发布到 PyPI
```bash
# 发布到 PyPI（需要先配置认证）
uv publish

# 发布到测试 PyPI
uv publish --repository testpypi

# 指定分发文件
uv publish dist/*
```

---

## 常用命令速查

### 项目管理
| 命令 | 说明 |
|------|------|
| `uv init` | 初始化新项目 |
| `uv sync` | 同步依赖 |
| `uv run <command>` | 在项目环境中运行命令 |
| `uv build` | 构建项目 |
| `uv publish` | 发布项目 |

### 依赖管理
| 命令 | 说明 |
|------|------|
| `uv add <package>` | 添加依赖 |
| `uv remove <package>` | 移除依赖 |
| `uv lock` | 更新锁文件 |
| `uv tree` | 显示依赖树 |

### 虚拟环境
| 命令 | 说明 |
|------|------|
| `uv venv` | 创建虚拟环境 |
| `uv python list` | 列出可用 Python 版本 |
| `uv python install <version>` | 安装 Python 版本 |

### 工具管理
| 命令 | 说明 |
|------|------|
| `uv tool install <package>` | 全局安装工具 |
| `uv tool run <package>` | 临时运行工具 |
| `uv tool list` | 列出已安装工具 |

---

## 实际使用示例

### 示例 1：创建 Web 应用项目
```bash
# 1. 创建新项目
uv init --app my-web-app
cd my-web-app

# 2. 添加 Web 框架依赖
uv add fastapi uvicorn

# 3. 添加开发依赖
uv add --dev pytest black ruff mypy

# 4. 创建简单的 FastAPI 应用
cat > src/my_web_app/main.py << 'EOF'
from fastapi import FastAPI

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}
EOF

# 5. 运行应用
uv run uvicorn src.my_web_app.main:app --reload
```

### 示例 2：数据科学项目
```bash
# 1. 创建项目
uv init data-analysis
cd data-analysis

# 2. 添加数据科学依赖
uv add pandas numpy matplotlib seaborn jupyter

# 3. 添加开发工具
uv add --dev pytest black ruff

# 4. 启动 Jupyter Notebook
uv run jupyter notebook
```

### 示例 3：从现有项目迁移
```bash
# 1. 在现有项目目录中初始化
uv init

# 2. 从 requirements.txt 导入依赖
uv add -r requirements.txt

# 3. 从 requirements-dev.txt 导入开发依赖
uv add --dev -r requirements-dev.txt

# 4. 同步环境
uv sync
```

### 示例 4：CLI 工具开发
```bash
# 1. 创建 CLI 工具项目
uv init --app my-cli-tool
cd my-cli-tool

# 2. 添加 CLI 相关依赖
uv add click rich typer

# 3. 添加开发和测试依赖
uv add --dev pytest pytest-cov black ruff mypy

# 4. 创建 CLI 入口文件
cat > src/my_cli_tool/cli.py << 'EOF'
import click
from rich.console import Console

console = Console()

@click.command()
@click.option('--name', default='World', help='要问候的名字')
@click.option('--count', default=1, help='问候次数')
def hello(name, count):
    """简单的问候程序"""
    for _ in range(count):
        console.print(f"Hello {name}!", style="bold green")

if __name__ == '__main__':
    hello()
EOF

# 5. 配置项目入口点
cat >> pyproject.toml << 'EOF'

[project.scripts]
my-cli = "my_cli_tool.cli:hello"
EOF

# 6. 安装项目（可编辑模式）
uv sync

# 7. 运行 CLI 工具
uv run my-cli --name "开发者" --count 3
```

### 示例 5：机器学习项目
```bash
# 1. 创建 ML 项目
uv init ml-project
cd ml-project

# 2. 添加机器学习依赖
uv add scikit-learn pandas numpy matplotlib seaborn

# 3. 添加深度学习依赖（可选组）
uv add --optional deep torch torchvision tensorflow

# 4. 添加开发依赖
uv add --dev jupyter notebook pytest black ruff mypy

# 5. 创建数据处理脚本
mkdir -p src/ml_project
cat > src/ml_project/data_processor.py << 'EOF'
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

class DataProcessor:
    def __init__(self):
        self.scaler = StandardScaler()
    
    def load_data(self, file_path):
        """加载数据"""
        return pd.read_csv(file_path)
    
    def preprocess(self, data, target_column):
        """数据预处理"""
        X = data.drop(columns=[target_column])
        y = data[target_column]
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # 标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        return X_train_scaled, X_test_scaled, y_train, y_test
EOF

# 6. 安装并运行
uv sync
uv run python -c "from src.ml_project.data_processor import DataProcessor; print('ML 项目设置完成！')"

# 7. 启动 Jupyter 进行实验
uv run jupyter notebook
```

### 示例 6：微服务项目
```bash
# 1. 创建微服务项目
uv init --app user-service
cd user-service

# 2. 添加微服务相关依赖
uv add fastapi uvicorn sqlalchemy alembic pydantic-settings

# 3. 添加数据库和缓存依赖
uv add psycopg2-binary redis

# 4. 添加开发和测试依赖
uv add --dev pytest pytest-asyncio httpx black ruff mypy

# 5. 创建项目结构
mkdir -p src/user_service/{api,models,services,database}

# 6. 创建数据库模型
cat > src/user_service/models/user.py << 'EOF'
from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
EOF

# 7. 创建 API 路由
cat > src/user_service/api/users.py << 'EOF'
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List

router = APIRouter()

class UserCreate(BaseModel):
    username: str
    email: str

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    is_active: bool

@router.post("/users/", response_model=UserResponse)
async def create_user(user: UserCreate):
    # 这里应该连接数据库创建用户
    return UserResponse(
        id=1,
        username=user.username,
        email=user.email,
        is_active=True
    )

@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(user_id: int):
    # 这里应该从数据库获取用户
    return UserResponse(
        id=user_id,
        username="test_user",
        email="<EMAIL>",
        is_active=True
    )
EOF

# 8. 创建主应用
cat > src/user_service/main.py << 'EOF'
from fastapi import FastAPI
from .api.users import router as users_router

app = FastAPI(title="User Service", version="1.0.0")

app.include_router(users_router, prefix="/api/v1")

@app.get("/health")
async def health_check():
    return {"status": "healthy"}
EOF

# 9. 运行微服务
uv run uvicorn src.user_service.main:app --reload --port 8001
```

### 示例 7：爬虫项目
```bash
# 1. 创建爬虫项目
uv init web-scraper
cd web-scraper

# 2. 添加爬虫相关依赖
uv add requests beautifulsoup4 scrapy selenium

# 3. 添加数据处理依赖
uv add pandas openpyxl

# 4. 添加异步支持
uv add aiohttp asyncio

# 5. 添加开发依赖
uv add --dev pytest black ruff mypy

# 6. 创建简单爬虫
cat > src/web_scraper/simple_scraper.py << 'EOF'
import requests
from bs4 import BeautifulSoup
import pandas as pd
from typing import List, Dict

class SimpleScraper:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def scrape_page(self, url: str) -> Dict:
        """爬取单个页面"""
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取标题
            title = soup.find('title')
            title_text = title.text.strip() if title else "无标题"
            
            return {
                'url': url,
                'title': title_text,
                'status': 'success'
            }
        except Exception as e:
            return {
                'url': url,
                'title': '',
                'status': f'error: {str(e)}'
            }
    
    def scrape_multiple(self, urls: List[str]) -> pd.DataFrame:
        """批量爬取"""
        results = []
        for url in urls:
            print(f"正在爬取: {url}")
            result = self.scrape_page(url)
            results.append(result)
        
        return pd.DataFrame(results)
    
    def save_to_excel(self, data: pd.DataFrame, filename: str):
        """保存到 Excel"""
        data.to_excel(filename, index=False)
        print(f"数据已保存到: {filename}")

# 使用示例
if __name__ == "__main__":
    scraper = SimpleScraper("https://example.com")
    urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/json",
    ]
    
    data = scraper.scrape_multiple(urls)
    scraper.save_to_excel(data, "scraping_results.xlsx")
EOF

# 7. 运行爬虫
uv run python src/web_scraper/simple_scraper.py
```

### 示例 8：测试驱动开发 (TDD)
```bash
# 1. 创建 TDD 项目
uv init --lib calculator
cd calculator

# 2. 添加测试相关依赖
uv add --dev pytest pytest-cov pytest-mock black ruff mypy

# 3. 创建测试文件（先写测试）
cat > tests/test_calculator.py << 'EOF'
import pytest
from src.calculator.calculator import Calculator

class TestCalculator:
    def setup_method(self):
        self.calc = Calculator()
    
    def test_add(self):
        assert self.calc.add(2, 3) == 5
        assert self.calc.add(-1, 1) == 0
        assert self.calc.add(0, 0) == 0
    
    def test_subtract(self):
        assert self.calc.subtract(5, 3) == 2
        assert self.calc.subtract(0, 5) == -5
    
    def test_multiply(self):
        assert self.calc.multiply(3, 4) == 12
        assert self.calc.multiply(-2, 3) == -6
        assert self.calc.multiply(0, 100) == 0
    
    def test_divide(self):
        assert self.calc.divide(10, 2) == 5
        assert self.calc.divide(7, 2) == 3.5
    
    def test_divide_by_zero(self):
        with pytest.raises(ValueError, match="不能除以零"):
            self.calc.divide(5, 0)
    
    def test_power(self):
        assert self.calc.power(2, 3) == 8
        assert self.calc.power(5, 0) == 1
    
    def test_sqrt(self):
        assert self.calc.sqrt(9) == 3
        assert self.calc.sqrt(16) == 4
    
    def test_sqrt_negative(self):
        with pytest.raises(ValueError, match="不能计算负数的平方根"):
            self.calc.sqrt(-1)
EOF

# 4. 运行测试（应该失败，因为还没有实现）
uv run pytest tests/ -v

# 5. 实现计算器类
cat > src/calculator/calculator.py << 'EOF'
import math

class Calculator:
    """简单的计算器类"""
    
    def add(self, a: float, b: float) -> float:
        """加法"""
        return a + b
    
    def subtract(self, a: float, b: float) -> float:
        """减法"""
        return a - b
    
    def multiply(self, a: float, b: float) -> float:
        """乘法"""
        return a * b
    
    def divide(self, a: float, b: float) -> float:
        """除法"""
        if b == 0:
            raise ValueError("不能除以零")
        return a / b
    
    def power(self, base: float, exponent: float) -> float:
        """幂运算"""
        return base ** exponent
    
    def sqrt(self, number: float) -> float:
        """平方根"""
        if number < 0:
            raise ValueError("不能计算负数的平方根")
        return math.sqrt(number)
EOF

# 6. 再次运行测试（应该通过）
uv run pytest tests/ -v --cov=src

# 7. 运行代码质量检查
uv run black src/ tests/
uv run ruff check src/ tests/
uv run mypy src/
```

### 示例 9：配置管理项目
```bash
# 1. 创建配置管理项目
uv init config-manager
cd config-manager

# 2. 添加配置相关依赖
uv add pydantic pydantic-settings python-dotenv pyyaml toml

# 3. 添加开发依赖
uv add --dev pytest black ruff mypy

# 4. 创建配置模型
cat > src/config_manager/settings.py << 'EOF'
from pydantic import BaseSettings, Field
from typing import Optional, List
import os

class DatabaseSettings(BaseSettings):
    """数据库配置"""
    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=5432, env="DB_PORT")
    username: str = Field(env="DB_USERNAME")
    password: str = Field(env="DB_PASSWORD")
    database: str = Field(env="DB_DATABASE")
    
    @property
    def url(self) -> str:
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"

class RedisSettings(BaseSettings):
    """Redis 配置"""
    host: str = Field(default="localhost", env="REDIS_HOST")
    port: int = Field(default=6379, env="REDIS_PORT")
    password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    db: int = Field(default=0, env="REDIS_DB")

class AppSettings(BaseSettings):
    """应用配置"""
    name: str = Field(default="MyApp", env="APP_NAME")
    version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    secret_key: str = Field(env="SECRET_KEY")
    allowed_hosts: List[str] = Field(default=["localhost"], env="ALLOWED_HOSTS")
    
    # 嵌套配置
    database: DatabaseSettings = DatabaseSettings()
    redis: RedisSettings = RedisSettings()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# 全局设置实例
settings = AppSettings()
EOF

# 5. 创建环境文件示例
cat > .env.example << 'EOF'
# 应用配置
APP_NAME=MyApp
APP_VERSION=1.0.0
DEBUG=false
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=myuser
DB_PASSWORD=mypassword
DB_DATABASE=mydatabase

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
EOF

# 6. 创建配置加载器
cat > src/config_manager/loader.py << 'EOF'
import yaml
import toml
import json
from pathlib import Path
from typing import Dict, Any

class ConfigLoader:
    """配置文件加载器"""
    
    @staticmethod
    def load_yaml(file_path: str) -> Dict[str, Any]:
        """加载 YAML 配置文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    @staticmethod
    def load_toml(file_path: str) -> Dict[str, Any]:
        """加载 TOML 配置文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return toml.load(f)
    
    @staticmethod
    def load_json(file_path: str) -> Dict[str, Any]:
        """加载 JSON 配置文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @classmethod
    def load_config(cls, file_path: str) -> Dict[str, Any]:
        """根据文件扩展名自动选择加载方式"""
        path = Path(file_path)
        suffix = path.suffix.lower()
        
        if suffix in ['.yml', '.yaml']:
            return cls.load_yaml(file_path)
        elif suffix == '.toml':
            return cls.load_toml(file_path)
        elif suffix == '.json':
            return cls.load_json(file_path)
        else:
            raise ValueError(f"不支持的配置文件格式: {suffix}")
EOF

# 7. 创建使用示例
cat > src/config_manager/example.py << 'EOF'
from .settings import settings
from .loader import ConfigLoader

def main():
    """配置使用示例"""
    print("=== 应用配置 ===")
    print(f"应用名称: {settings.name}")
    print(f"版本: {settings.version}")
    print(f"调试模式: {settings.debug}")
    print(f"允许的主机: {settings.allowed_hosts}")
    
    print("\n=== 数据库配置 ===")
    print(f"数据库 URL: {settings.database.url}")
    
    print("\n=== Redis 配置 ===")
    print(f"Redis 主机: {settings.redis.host}")
    print(f"Redis 端口: {settings.redis.port}")
    
    # 加载外部配置文件示例
    try:
        config = ConfigLoader.load_config("config.yaml")
        print(f"\n=== 外部配置 ===")
        print(config)
    except FileNotFoundError:
        print("\n外部配置文件不存在，跳过加载")

if __name__ == "__main__":
    main()
EOF

# 8. 创建测试配置文件
cat > config.yaml << 'EOF'
app:
  name: "测试应用"
  version: "2.0.0"
  features:
    - "用户管理"
    - "数据分析"
    - "报表生成"

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
EOF

# 9. 运行示例
cp .env.example .env
# 编辑 .env 文件填入实际配置
uv run python src/config_manager/example.py
```

### 示例 10：异步任务队列
```bash
# 1. 创建任务队列项目
uv init task-queue
cd task-queue

# 2. 添加异步和队列相关依赖
uv add celery redis asyncio aioredis

# 3. 添加 Web 框架
uv add fastapi uvicorn

# 4. 添加开发依赖
uv add --dev pytest pytest-asyncio black ruff mypy

# 5. 创建 Celery 配置
cat > src/task_queue/celery_app.py << 'EOF'
from celery import Celery
import time
import requests

# 创建 Celery 应用
app = Celery(
    'task_queue',
    broker='redis://localhost:6379/0',
    backend='redis://localhost:6379/0'
)

# 配置
app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    result_expires=3600,
)

@app.task
def add_numbers(x: int, y: int) -> int:
    """简单的加法任务"""
    time.sleep(2)  # 模拟耗时操作
    return x + y

@app.task
def send_email(to: str, subject: str, body: str) -> dict:
    """发送邮件任务"""
    print(f"发送邮件到: {to}")
    print(f"主题: {subject}")
    print(f"内容: {body}")
    
    # 模拟发送邮件
    time.sleep(3)
    
    return {
        "status": "sent",
        "to": to,
        "subject": subject,
        "sent_at": time.time()
    }

@app.task
def fetch_url(url: str) -> dict:
    """获取网页内容任务"""
    try:
        response = requests.get(url, timeout=10)
        return {
            "url": url,
            "status_code": response.status_code,
            "content_length": len(response.content),
            "success": True
        }
    except Exception as e:
        return {
            "url": url,
            "error": str(e),
            "success": False
        }

@app.task(bind=True)
def long_running_task(self, duration: int):
    """长时间运行的任务，支持进度更新"""
    for i in range(duration):
        time.sleep(1)
        # 更新任务进度
        self.update_state(
            state='PROGRESS',
            meta={'current': i + 1, 'total': duration}
        )
    
    return {'status': 'completed', 'duration': duration}
EOF

# 6. 创建 FastAPI 应用
cat > src/task_queue/api.py << 'EOF'
from fastapi import FastAPI, BackgroundTasks
from pydantic import BaseModel
from .celery_app import add_numbers, send_email, fetch_url, long_running_task
from celery.result import AsyncResult
from typing import Dict, Any

app = FastAPI(title="任务队列 API", version="1.0.0")

class TaskResponse(BaseModel):
    task_id: str
    status: str
    message: str

class EmailTask(BaseModel):
    to: str
    subject: str
    body: str

@app.post("/tasks/add", response_model=TaskResponse)
async def create_add_task(x: int, y: int):
    """创建加法任务"""
    task = add_numbers.delay(x, y)
    return TaskResponse(
        task_id=task.id,
        status="pending",
        message=f"加法任务已创建: {x} + {y}"
    )

@app.post("/tasks/email", response_model=TaskResponse)
async def create_email_task(email: EmailTask):
    """创建发送邮件任务"""
    task = send_email.delay(email.to, email.subject, email.body)
    return TaskResponse(
        task_id=task.id,
        status="pending",
        message=f"邮件任务已创建，发送到: {email.to}"
    )

@app.post("/tasks/fetch", response_model=TaskResponse)
async def create_fetch_task(url: str):
    """创建网页获取任务"""
    task = fetch_url.delay(url)
    return TaskResponse(
        task_id=task.id,
        status="pending",
        message=f"网页获取任务已创建: {url}"
    )

@app.post("/tasks/long-running", response_model=TaskResponse)
async def create_long_running_task(duration: int):
    """创建长时间运行任务"""
    task = long_running_task.delay(duration)
    return TaskResponse(
        task_id=task.id,
        status="pending",
        message=f"长时间任务已创建，持续时间: {duration}秒"
    )

@app.get("/tasks/{task_id}")
async def get_task_status(task_id: str) -> Dict[str, Any]:
    """获取任务状态"""
    result = AsyncResult(task_id)
    
    if result.state == 'PENDING':
        return {
            "task_id": task_id,
            "status": "pending",
            "message": "任务正在等待执行"
        }
    elif result.state == 'PROGRESS':
        return {
            "task_id": task_id,
            "status": "progress",
            "current": result.info.get('current', 0),
            "total": result.info.get('total', 1),
            "message": "任务正在执行中"
        }
    elif result.state == 'SUCCESS':
        return {
            "task_id": task_id,
            "status": "success",
            "result": result.result,
            "message": "任务执行成功"
        }
    else:
        return {
            "task_id": task_id,
            "status": "failed",
            "error": str(result.info),
            "message": "任务执行失败"
        }

@app.get("/")
async def root():
    return {"message": "任务队列 API 服务正在运行"}
EOF

# 7. 创建启动脚本
cat > scripts/start_worker.sh << 'EOF'
#!/bin/bash
echo "启动 Celery Worker..."
cd "$(dirname "$0")/.."
uv run celery -A src.task_queue.celery_app worker --loglevel=info
EOF

cat > scripts/start_api.sh << 'EOF'
#!/bin/bash
echo "启动 FastAPI 服务..."
cd "$(dirname "$0")/.."
uv run uvicorn src.task_queue.api:app --reload --port 8000
EOF

chmod +x scripts/*.sh

# 8. 创建使用示例
cat > examples/client_example.py << 'EOF'
import requests
import time
import json

API_BASE = "http://localhost:8000"

def test_add_task():
    """测试加法任务"""
    print("=== 测试加法任务 ===")
    response = requests.post(f"{API_BASE}/tasks/add?x=10&y=20")
    task_data = response.json()
    task_id = task_data["task_id"]
    print(f"任务 ID: {task_id}")
    
    # 等待任务完成
    while True:
        status_response = requests.get(f"{API_BASE}/tasks/{task_id}")
        status_data = status_response.json()
        print(f"任务状态: {status_data['status']}")
        
        if status_data["status"] == "success":
            print(f"计算结果: {status_data['result']}")
            break
        elif status_data["status"] == "failed":
            print(f"任务失败: {status_data['error']}")
            break
# uv Python 包管理工具学习指南

## 📚 目录

1. [uv 简介](#uv-简介)
2. [安装 uv](#安装-uv)
3. [基础概念](#基础概念)
4. [项目初始化](#项目初始化)
5. [依赖管理](#依赖管理)
6. [虚拟环境操作](#虚拟环境操作)
7. [构建和发布](#构建和发布)
8. [常用命令速查](#常用命令速查)
9. [实际使用示例](#实际使用示例)
10. [最佳实践](#最佳实践)
11. [常见问题解决](#常见问题解决)

---

## uv 简介

uv 是由 Astral 开发的极速 Python 包管理工具，用 Rust 编写，旨在替代 pip、pip-tools、pipx、poetry、pyenv、virtualenv 等工具。

### 🚀 主要优势

- **极速性能**：比 pip 快 10-100 倍
- **统一工具**：集成包管理、虚拟环境、项目管理于一体
- **兼容性强**：与现有 Python 生态系统完全兼容
- **内存高效**：使用 Rust 编写，内存占用低
- **跨平台**：支持 Windows、macOS、Linux

---

## 安装 uv

### 通过官方安装脚本（推荐）
```bash
# macOS 和 Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 通过包管理器
```bash
# macOS (Homebrew)
brew install uv

# Windows (Scoop)
scoop install uv

# 通过 pip 安装
pip install uv
```

### 验证安装
```bash
uv --version
```

---

## 基础概念

### 核心组件
- **项目 (Project)**：包含 `pyproject.toml` 的 Python 项目
- **虚拟环境 (Virtual Environment)**：隔离的 Python 环境
- **锁文件 (Lock File)**：`uv.lock` 文件，记录精确的依赖版本
- **工作区 (Workspace)**：包含多个相关项目的集合

### 配置文件
- `pyproject.toml`：项目配置和依赖声明
- `uv.lock`：锁定的依赖版本（自动生成）
- `uv.toml`：uv 工具配置文件

---

## 项目初始化

### 创建新项目
```bash
# 创建新的 Python 项目
uv init my-project
cd my-project

# 创建应用程序项目
uv init --app my-app

# 创建库项目
uv init --lib my-library

# 在现有目录初始化
uv init
```

### 项目结构
```
my-project/
├── pyproject.toml      # 项目配置
├── uv.lock            # 锁文件
├── README.md          # 项目说明
├── src/               # 源代码目录
│   └── my_project/
│       └── __init__.py
└── tests/             # 测试目录
```

---

## 依赖管理

### 添加依赖
```bash
# 添加运行时依赖
uv add requests
uv add "django>=4.0"
uv add "numpy>=1.20,<2.0"

# 添加开发依赖
uv add --dev pytest
uv add --dev black ruff mypy

# 添加可选依赖组
uv add --optional web fastapi uvicorn
uv add --optional dev pytest black ruff

# 从 requirements.txt 添加
uv add -r requirements.txt
```

### 移除依赖
```bash
# 移除依赖
uv remove requests
uv remove --dev pytest

# 移除可选依赖组
uv remove --optional web fastapi
```

### 安装依赖
```bash
# 安装所有依赖
uv sync

# 只安装生产依赖
uv sync --no-dev

# 安装特定可选依赖组
uv sync --extra web

# 安装所有可选依赖
uv sync --all-extras
```

### 更新依赖
```bash
# 更新所有依赖
uv lock --upgrade

# 更新特定包
uv lock --upgrade-package requests

# 查看过期的包
uv tree --outdated
```

---

## 虚拟环境操作

### 创建和激活虚拟环境
```bash
# 创建虚拟环境
uv venv

# 指定 Python 版本
uv venv --python 3.11
uv venv --python python3.12

# 指定虚拟环境位置
uv venv .venv
uv venv /path/to/venv

# 激活虚拟环境
# Linux/macOS
source .venv/bin/activate
# Windows
.venv\Scripts\activate
```

### 在虚拟环境中运行命令
```bash
# 直接在项目环境中运行
uv run python script.py
uv run pytest
uv run python -m mymodule

# 运行 Python 脚本
uv run --script my_script.py

# 临时安装并运行工具
uv tool run black .
uv tool run ruff check .
```

---

## 构建和发布

### 构建项目
```bash
# 构建分发包
uv build

# 构建到指定目录
uv build --out-dir dist/

# 只构建 wheel
uv build --wheel

# 只构建源码分发
uv build --sdist
```

### 发布到 PyPI
```bash
# 发布到 PyPI（需要先配置认证）
uv publish

# 发布到测试 PyPI
uv publish --repository testpypi

# 指定分发文件
uv publish dist/*
```

---

## 常用命令速查

### 项目管理
| 命令 | 说明 |
|------|------|
| `uv init` | 初始化新项目 |
| `uv sync` | 同步依赖 |
| `uv run <command>` | 在项目环境中运行命令 |
| `uv build` | 构建项目 |
| `uv publish` | 发布项目 |

### 依赖管理
| 命令 | 说明 |
|------|------|
| `uv add <package>` | 添加依赖 |
| `uv remove <package>` | 移除依赖 |
| `uv lock` | 更新锁文件 |
| `uv tree` | 显示依赖树 |

### 虚拟环境
| 命令 | 说明 |
|------|------|
| `uv venv` | 创建虚拟环境 |
| `uv python list` | 列出可用 Python 版本 |
| `uv python install <version>` | 安装 Python 版本 |

### 工具管理
| 命令 | 说明 |
|------|------|
| `uv tool install <package>` | 全局安装工具 |
| `uv tool run <package>` | 临时运行工具 |
| `uv tool list` | 列出已安装工具 |

---

## 实际使用示例

### 示例 1：创建 Web 应用项目
```bash
# 1. 创建新项目
uv init --app my-web-app
cd my-web-app

# 2. 添加 Web 框架依赖
uv add fastapi uvicorn

# 3. 添加开发依赖
uv add --dev pytest black ruff mypy

# 4. 创建简单的 FastAPI 应用
cat > src/my_web_app/main.py << 'EOF'
from fastapi import FastAPI

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}
EOF

# 5. 运行应用
uv run uvicorn src.my_web_app.main:app --reload
```

### 示例 2：数据科学项目
```bash
# 1. 创建项目
uv init data-analysis
cd data-analysis

# 2. 添加数据科学依赖
uv add pandas numpy matplotlib seaborn jupyter

# 3. 添加开发工具
uv add --dev pytest black ruff

# 4. 启动 Jupyter Notebook
uv run jupyter notebook
```

### 示例 3：从现有项目迁移
```bash
# 1. 在现有项目目录中初始化
uv init

# 2. 从 requirements.txt 导入依赖
uv add -r requirements.txt

# 3. 从 requirements-dev.txt 导入开发依赖
uv add --dev -r requirements-dev.txt

# 4. 同步环境
uv sync
```

---

## 最佳实践

### 1. 项目结构组织
```
my-project/
├── pyproject.toml      # 项目配置
├── uv.lock            # 锁文件（提交到版本控制）
├── .python-version     # Python 版本固定
├── src/               # 源代码
├── tests/             # 测试代码
├── docs/              # 文档
└── scripts/           # 脚本文件
```

### 2. pyproject.toml 配置示例
```toml
[project]
name = "my-project"
version = "0.1.0"
description = "My awesome project"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
dependencies = [
    "requests>=2.25.0",
    "click>=8.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black>=22.0",
    "ruff>=0.1.0",
    "mypy>=1.0",
]
web = [
    "fastapi>=0.68.0",
    "uvicorn>=0.15.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=6.0",
    "black>=22.0",
    "ruff>=0.1.0",
]
```

### 3. 工作流建议
1. **始终使用锁文件**：提交 `uv.lock` 到版本控制
2. **固定 Python 版本**：使用 `.python-version` 文件
3. **分离依赖类型**：区分运行时、开发、可选依赖
4. **定期更新**：使用 `uv lock --upgrade` 更新依赖
5. **使用 uv run**：避免手动激活虚拟环境

### 4. CI/CD 集成
```yaml
# GitHub Actions 示例
name: CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Install uv
      uses: astral-sh/setup-uv@v1
    - name: Set up Python
      run: uv python install
    - name: Install dependencies
      run: uv sync --all-extras --dev
    - name: Run tests
      run: uv run pytest
```

---

## 常见问题解决

### Q1: 如何迁移现有的 pip/poetry 项目？
```bash
# 从 requirements.txt 迁移
uv init
uv add -r requirements.txt
uv add --dev -r requirements-dev.txt

# 从 poetry 迁移
uv init
# 手动将 pyproject.toml 中的依赖复制过来，或者
uv add $(poetry show --only=main --no-dev | awk '{print $1}')
```

### Q2: 如何处理私有包索引？
```bash
# 添加私有索引
uv add --index-url https://private.pypi.org/simple/ my-private-package

# 在 pyproject.toml 中配置
[tool.uv]
index-url = "https://private.pypi.org/simple/"
extra-index-url = ["https://pypi.org/simple/"]
```

### Q3: 如何固定 Python 版本？
```bash
# 创建 .python-version 文件
echo "3.11" > .python-version

# 或在 pyproject.toml 中指定
[project]
requires-python = ">=3.11"
```

### Q4: 虚拟环境在哪里？
```bash
# 查看虚拟环境位置
uv venv --show-path

# 默认位置：
# - 项目根目录的 .venv/
# - 或者 ~/.local/share/uv/env/
```

### Q5: 如何清理缓存？
```bash
# 清理包缓存
uv cache clean

# 查看缓存大小
uv cache dir
```

### Q6: 如何处理依赖冲突？
```bash
# 查看依赖树找出冲突
uv tree

# 使用 --resolution 策略
uv sync --resolution lowest-direct  # 使用最低版本
uv sync --resolution highest        # 使用最高版本
```

---

## 🎯 总结

uv 是现代 Python 开发的强大工具，它简化了包管理、虚拟环境和项目管理的复杂性。通过掌握这些常用命令和最佳实践，你可以：

- 🚀 显著提升开发效率
- 📦 更好地管理项目依赖
- 🔧 简化开发工作流
- 🛡️ 确保环境一致性

记住：**多实践，多尝试**，uv 的强大功能需要在实际项目中才能充分体现！

---

*最后更新：2024年*
*作者：Claude 4.0 sonnet*