#!/usr/bin/env python3
"""
配置验证和测试脚本
用于验证环境变量配置是否正确，并进行基本的功能测试
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

def test_environment_variables():
    """测试必需的环境变量是否存在"""
    print("🔍 检查环境变量配置...")
    
    required_vars = [
        "API_BASE_URL",
        "API_AUTHORIZATION_TOKEN"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            # 对敏感信息进行部分掩码
            if "token" in var.lower():
                masked_value = f"{value[:8]}...{value[-4:]}" if len(value) > 12 else "***"
                print(f"  ✅ {var}: {masked_value}")
            else:
                print(f"  ✅ {var}: {value}")
    
    if missing_vars:
        print(f"  ❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
        return False
    
    print("  ✅ 所有必需的环境变量都已设置")
    return True

def test_config_loading():
    """测试配置加载"""
    print("\n⚙️ 测试配置加载...")
    
    try:
        from py_test.config.settings import get_settings
        settings = get_settings()
        
        print("  ✅ 配置模块加载成功")
        print(f"  📊 配置摘要:\n{settings.get_summary()}")
        
        return True
    except Exception as e:
        print(f"  ❌ 配置加载失败: {e}")
        return False

def test_user_codes_file():
    """测试用户代码文件"""
    print("\n📄 测试用户代码文件...")
    
    try:
        from py_test.config.settings import get_settings
        settings = get_settings()
        
        user_codes = settings.load_user_codes()
        print(f"  ✅ 用户代码文件加载成功")
        print(f"  📊 用户代码数量: {len(user_codes)}")
        print(f"  📋 前5个用户代码: {user_codes[:5]}")
        
        return True
    except Exception as e:
        print(f"  ❌ 用户代码文件加载失败: {e}")
        return False

def test_dotenv_installation():
    """测试python-dotenv是否正确安装"""
    print("\n📦 测试python-dotenv安装...")
    
    try:
        import dotenv
        print(f"  ✅ python-dotenv已安装，版本: {dotenv.__version__}")
        return True
    except ImportError:
        print("  ⚠️ python-dotenv未安装，将使用系统环境变量")
        print("  💡 建议运行: uv add python-dotenv")
        return False

def test_output_permissions():
    """测试输出文件写入权限"""
    print("\n📝 测试输出文件权限...")
    
    try:
        from py_test.config.settings import get_settings
        settings = get_settings()
        
        # 测试创建输出文件
        test_files = [
            settings.output.log_file,
            settings.output.responses_file,
            settings.output.report_file
        ]
        
        for file_path in test_files:
            try:
                with open(file_path, 'a', encoding=settings.output.log_encoding):
                    pass
                print(f"  ✅ {file_path}: 写入权限正常")
            except Exception as e:
                print(f"  ❌ {file_path}: 写入权限错误 - {e}")
                return False
        
        return True
    except Exception as e:
        print(f"  ❌ 输出权限测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始配置验证和测试\n")
    
    tests = [
        ("环境变量检查", test_environment_variables),
        ("dotenv安装检查", test_dotenv_installation),
        ("配置加载测试", test_config_loading),
        ("用户代码文件测试", test_user_codes_file),
        ("输出权限测试", test_output_permissions)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  💥 {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！配置正确，可以运行应用程序。")
        print("\n💡 下一步:")
        print("  1. 运行: python batch_api_runner.py")
        print("  2. 检查输出文件: summary_report.txt")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置。")
        print("\n🔧 常见解决方案:")
        print("  1. 检查 .env 文件是否存在且配置正确")
        print("  2. 运行: uv sync 安装依赖")
        print("  3. 确保 data/user_codes.txt 文件存在")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)