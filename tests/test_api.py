"""
API模块测试
"""

import unittest
import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from py_test.api.client import APIClient
from py_test.api.runner import BatchRunner


class TestAPIClient(unittest.TestCase):
    """测试API客户端"""
    
    def setUp(self):
        """测试前准备"""
        self.client = APIClient()
    
    def test_client_initialization(self):
        """测试客户端初始化"""
        self.assertIsNotNone(self.client.settings)
        self.assertIsNotNone(self.client.logger)
    
    def test_request_structure(self):
        """测试请求结构（不发送实际请求）"""
        # 这里可以添加mock测试
        pass


class TestBatchRunner(unittest.TestCase):
    """测试批处理运行器"""
    
    def setUp(self):
        """测试前准备"""
        self.runner = BatchRunner()
    
    def test_runner_initialization(self):
        """测试运行器初始化"""
        self.assertIsNotNone(self.runner.settings)
        self.assertIsNotNone(self.runner.client)
        self.assertIsNotNone(self.runner.logger)
        self.assertEqual(len(self.runner.responses), 0)


if __name__ == "__main__":
    unittest.main()