[project]
name = "py-test"
version = "0.1.0"
description = "Python批处理API客户端 - 用于批量同步用户数据到OA系统"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "python-dotenv>=1.0.0",
    "requests>=2.31.0"
]

[project.scripts]
run-batch = "py_test.scripts.run_batch:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/py_test"]
